This is XeTeX, Version 3.141592653-2.6-0.999993 (TeX Live 2021/W32TeX) (preloaded format=xelatex 2021.4.6)  26 JUL 2025 16:36
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**seminar.tex
(./seminar.tex
LaTeX2e <2020-10-01> patch level 4
L3 programming layer <2021-02-18>
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/base/article.c
ls
Document Class: article 2020/04/10 v1.4m Standard LaTeX document class

(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/base/size12.cl
o
File: size12.clo 2020/04/10 v1.4m Standard LaTeX file (size option)
)
\c@part=\count175
\c@section=\count176
\c@subsection=\count177
\c@subsubsection=\count178
\c@paragraph=\count179
\c@subparagraph=\count180
\c@figure=\count181
\c@table=\count182
\abovecaptionskip=\skip47
\belowcaptionskip=\skip48
\bibindent=\dimen138
)
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/ctex/ctex.sty
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/l3kernel/expl3
.sty
Package: expl3 2021-02-18 L3 programming layer (loader) 

(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/l3backend/l3ba
ckend-xetex.def
File: l3backend-xetex.def 2021-03-18 L3 backend support: XeTeX
 (|extractbb --version)
\c__kernel_sys_dvipdfmx_version_int=\count183
\l__color_backend_stack_int=\count184
\g__color_backend_stack_int=\count185
\g__graphics_track_int=\count186
\l__pdf_internal_box=\box47
\g__pdf_backend_object_int=\count187
\g__pdf_backend_annotation_int=\count188
\g__pdf_backend_link_int=\count189
))
Package: ctex 2021/03/14 v2.5.6 Chinese adapter in LaTeX (CTEX)

(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/l3packages/xpa
rse/xparse.sty
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/l3packages/xpa
rse/xparse-2020-10-01.sty
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/l3packages/xpa
rse/xparse-generic.tex)))
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/l3packages/l3k
eys2e/l3keys2e.sty
Package: l3keys2e 2021-03-12 LaTeX2e option processing using LaTeX3 keys
)
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/ctex/ctexhook.
sty
Package: ctexhook 2021/03/14 v2.5.6 Document and package hooks (CTEX)
)
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/ctex/ctexpatch
.sty
Package: ctexpatch 2021/03/14 v2.5.6 Patching commands (CTEX)
)
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/base/fix-cm.st
y
Package: fix-cm 2015/01/14 v1.1t fixes to LaTeX

(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/base/ts1enc.de
f
File: ts1enc.def 2001/06/05 v3.0e (jk/car/fm) Standard LaTeX file
LaTeX Font Info:    Redeclaring font encoding TS1 on input line 47.
))
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/everysel/every
sel.sty
Package: everysel 2021/01/20 v2.1 EverySelectfont Package (MS)

(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/everysel/every
sel-2011-10-28.sty))
\l__ctex_tmp_int=\count190
\l__ctex_tmp_box=\box48
\l__ctex_tmp_dim=\dimen139
\g__ctex_section_depth_int=\count191
\g__ctex_font_size_int=\count192

(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/ctex/config/ct
exopts.cfg
File: ctexopts.cfg 2021/03/14 v2.5.6 Option configuration file (CTEX)
)
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/ctex/engine/ct
ex-engine-xetex.def
File: ctex-engine-xetex.def 2021/03/14 v2.5.6 XeLaTeX adapter (CTEX)

(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/xelatex/xecjk/xeCJK.
sty
Package: xeCJK 2020/10/19 v3.8.6 Typesetting CJK scripts with XeLaTeX

(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/l3packages/xte
mplate/xtemplate.sty
Package: xtemplate 2021-03-12 L3 Experimental prototype document functions
\l__xtemplate_tmp_dim=\dimen140
\l__xtemplate_tmp_int=\count193
\l__xtemplate_tmp_muskip=\muskip16
\l__xtemplate_tmp_skip=\skip49
)
\l__xeCJK_tmp_int=\count194
\l__xeCJK_tmp_box=\box49
\l__xeCJK_tmp_dim=\dimen141
\l__xeCJK_tmp_skip=\skip50
\g__xeCJK_space_factor_int=\count195
\l__xeCJK_begin_int=\count196
\l__xeCJK_end_int=\count197
\c__xeCJK_CJK_class_int=\XeTeXcharclass1
\c__xeCJK_FullLeft_class_int=\XeTeXcharclass2
\c__xeCJK_FullRight_class_int=\XeTeXcharclass3
\c__xeCJK_HalfLeft_class_int=\XeTeXcharclass4
\c__xeCJK_HalfRight_class_int=\XeTeXcharclass5
\c__xeCJK_NormalSpace_class_int=\XeTeXcharclass6
\c__xeCJK_CM_class_int=\XeTeXcharclass7
\c__xeCJK_HangulJamo_class_int=\XeTeXcharclass8
\l__xeCJK_last_skip=\skip51
\g__xeCJK_node_int=\count198
\c__xeCJK_CJK_node_dim=\dimen142
\c__xeCJK_CJK-space_node_dim=\dimen143
\c__xeCJK_default_node_dim=\dimen144
\c__xeCJK_default-space_node_dim=\dimen145
\c__xeCJK_CJK-widow_node_dim=\dimen146
\c__xeCJK_normalspace_node_dim=\dimen147
\l__xeCJK_ccglue_skip=\skip52
\l__xeCJK_ecglue_skip=\skip53
\l__xeCJK_punct_kern_skip=\skip54
\l__xeCJK_last_penalty_int=\count199
\l__xeCJK_last_bound_dim=\dimen148
\l__xeCJK_last_kern_dim=\dimen149
\l__xeCJK_widow_penalty_int=\count266

Package xtemplate Info: Declaring object type 'xeCJK/punctuation' taking 0
(xtemplate)             argument(s) on line 2341.

\l__xeCJK_fixed_punct_width_dim=\dimen150
\l__xeCJK_mixed_punct_width_dim=\dimen151
\l__xeCJK_middle_punct_width_dim=\dimen152
\l__xeCJK_fixed_margin_width_dim=\dimen153
\l__xeCJK_mixed_margin_width_dim=\dimen154
\l__xeCJK_middle_margin_width_dim=\dimen155
\l__xeCJK_bound_punct_width_dim=\dimen156
\l__xeCJK_bound_margin_width_dim=\dimen157
\l__xeCJK_margin_minimum_dim=\dimen158
\l__xeCJK_kerning_total_width_dim=\dimen159
\l__xeCJK_same_align_margin_dim=\dimen160
\l__xeCJK_different_align_margin_dim=\dimen161
\l__xeCJK_kerning_margin_width_dim=\dimen162
\l__xeCJK_kerning_margin_minimum_dim=\dimen163
\l__xeCJK_bound_dim=\dimen164
\l__xeCJK_reverse_bound_dim=\dimen165
\l__xeCJK_margin_dim=\dimen166
\l__xeCJK_minimum_bound_dim=\dimen167
\l__xeCJK_kerning_margin_dim=\dimen168
\g__xeCJK_family_int=\count267
\l__xeCJK_fam_int=\count268
\g__xeCJK_fam_allocation_int=\count269
\l__xeCJK_verb_case_int=\count270
\l__xeCJK_verb_exspace_skip=\skip55

(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/fontspec/fonts
pec.sty
Package: fontspec 2020/02/21 v2.7i Font selection for XeLaTeX and LuaLaTeX

(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/fontspec/fonts
pec-xetex.sty
Package: fontspec-xetex 2020/02/21 v2.7i Font selection for XeLaTeX and LuaLaTe
X
\l__fontspec_script_int=\count271
\l__fontspec_language_int=\count272
\l__fontspec_strnum_int=\count273
\l__fontspec_tmp_int=\count274
\l__fontspec_tmpa_int=\count275
\l__fontspec_tmpb_int=\count276
\l__fontspec_tmpc_int=\count277
\l__fontspec_em_int=\count278
\l__fontspec_emdef_int=\count279
\l__fontspec_strong_int=\count280
\l__fontspec_strongdef_int=\count281
\l__fontspec_tmpa_dim=\dimen169
\l__fontspec_tmpb_dim=\dimen170
\l__fontspec_tmpc_dim=\dimen171

(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/base/fontenc.s
ty
Package: fontenc 2020/08/10 v2.0s Standard LaTeX package
)
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/fontspec/fonts
pec.cfg)))
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/xelatex/xecjk/xeCJK.
cfg
File: xeCJK.cfg 2020/10/19 v3.8.6 Configuration file for xeCJK package
))
\ccwd=\dimen172
\l__ctex_ccglue_skip=\skip56
)
\l__ctex_ziju_dim=\dimen173

(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/zhnumber/zhnum
ber.sty
Package: zhnumber 2020/05/01 v2.8 Typesetting numbers with Chinese glyphs
\l__zhnum_scale_int=\count282

(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/zhnumber/zhnum
ber-utf8.cfg
File: zhnumber-utf8.cfg 2020/05/01 v2.8 Chinese numerals with UTF8 encoding
))
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/ctex/scheme/ct
ex-scheme-chinese.def
File: ctex-scheme-chinese.def 2021/03/14 v2.5.6 Chinese scheme for generic (CTE
X)

(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/ctex/config/ct
ex-name-utf8.cfg
File: ctex-name-utf8.cfg 2021/03/14 v2.5.6 Caption with encoding UTF-8 (CTEX)
))
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/tools/indentfi
rst.sty
Package: indentfirst 1995/11/23 v1.03 Indent first paragraph (DPC)
)
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/ctex/fontset/c
tex-fontset-fandol.def
File: ctex-fontset-fandol.def 2021/03/14 v2.5.6 Fandol fonts definition (CTEX)


Package fontspec Warning: Font "FandolSong-Regular" does not contain requested
(fontspec)                Script "CJK".


Package fontspec Info: Font family 'FandolSong-Regular(0)' created for font
(fontspec)             'FandolSong-Regular' with options
(fontspec)             [Script={CJK},Extension={.otf},BoldFont={FandolSong-Bold
},ItalicFont={FandolKai-Regular}].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <->"[FandolSong-Regular.otf]/OT:language=dflt;"
(fontspec)             - 'small caps'  (m/sc) with NFSS spec.: 
(fontspec)             - 'bold' (b/n) with NFSS spec.:
(fontspec)             <->"[FandolSong-Bold.otf]/OT:language=dflt;"
(fontspec)             - 'bold small caps'  (b/sc) with NFSS spec.: 
(fontspec)             - 'italic' (m/it) with NFSS spec.:
(fontspec)             <->"[FandolKai-Regular.otf]/OT:language=dflt;"
(fontspec)             - 'italic small caps'  (m/scit) with NFSS spec.: 

))
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/ctex/config/ct
ex.cfg
File: ctex.cfg 2021/03/14 v2.5.6 Configuration file (CTEX)
)
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/amsmath/amsmat
h.sty
Package: amsmath 2020/09/23 v2.17i AMS math features
\@mathmargin=\skip57

For additional information on amsmath, use the `?' option.

(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/amsmath/amstex
t.sty
Package: amstext 2000/06/29 v2.01 AMS text

(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/amsmath/amsgen
.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks15
\ex@=\dimen174
))
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/amsmath/amsbsy
.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen175
)
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/amsmath/amsopn
.sty
Package: amsopn 2016/03/08 v2.02 operator names
)
\inf@bad=\count283
LaTeX Info: Redefining \frac on input line 234.
\uproot@=\count284
\leftroot@=\count285
LaTeX Info: Redefining \overline on input line 399.
\classnum@=\count286
\DOTSCASE@=\count287
LaTeX Info: Redefining \ldots on input line 496.
LaTeX Info: Redefining \dots on input line 499.
LaTeX Info: Redefining \cdots on input line 620.
\Mathstrutbox@=\box50
\strutbox@=\box51
\big@size=\dimen176
LaTeX Font Info:    Redeclaring font encoding OML on input line 743.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 744.
\macc@depth=\count288
\c@MaxMatrixCols=\count289
\dotsspace@=\muskip17
\c@parentequation=\count290
\dspbrk@lvl=\count291
\tag@help=\toks16
\row@=\count292
\column@=\count293
\maxfields@=\count294
\andhelp@=\toks17
\eqnshift@=\dimen177
\alignsep@=\dimen178
\tagshift@=\dimen179
\tagwidth@=\dimen180
\totwidth@=\dimen181
\lineht@=\dimen182
\@envbody=\toks18
\multlinegap=\skip58
\multlinetaggap=\skip59
\mathdisplay@stack=\toks19
LaTeX Info: Redefining \[ on input line 2923.
LaTeX Info: Redefining \] on input line 2924.
)
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/amsfonts/amssy
mb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols

(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/amsfonts/amsfo
nts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
))
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/graphics/graph
icx.sty
Package: graphicx 2020/09/09 v1.2b Enhanced LaTeX Graphics (DPC,SPQR)

(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/graphics/keyva
l.sty
Package: keyval 2014/10/28 v1.15 key=value parser (DPC)
\KV@toks@=\toks20
)
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/graphics/graph
ics.sty
Package: graphics 2020/08/30 v1.4c Standard LaTeX Graphics (DPC,SPQR)

(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/graphics/trig.
sty
Package: trig 2016/01/03 v1.10 sin cos tan (DPC)
)
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/graphics-cfg/g
raphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: xetex.def on input line 105.

(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/graphics-def/x
etex.def
File: xetex.def 2021/03/18 v5.0k Graphics/color driver for xetex
))
\Gin@req@height=\dimen183
\Gin@req@width=\dimen184
)
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/pgf/frontendla
yer/tikz.sty
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/pgf/basiclayer
/pgf.sty
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/pgf/utilities/
pgfrcs.sty
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/pgf/utilitie
s/pgfutil-common.tex
\pgfutil@everybye=\toks21
\pgfutil@tempdima=\dimen185
\pgfutil@tempdimb=\dimen186

(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/pgf/utilitie
s/pgfutil-common-lists.tex))
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/pgf/utilitie
s/pgfutil-latex.def
\pgfutil@abb=\box52
)
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/pgf/utilitie
s/pgfrcs.code.tex
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/pgf/pgf.revi
sion.tex)
Package: pgfrcs 2020/12/27 v3.1.8b (3.1.8b)
))
Package: pgf 2020/12/27 v3.1.8b (3.1.8b)

(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/pgf/basiclayer
/pgfcore.sty
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/pgf/systemlaye
r/pgfsys.sty
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/pgf/systemla
yer/pgfsys.code.tex
Package: pgfsys 2020/12/27 v3.1.8b (3.1.8b)

(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/pgf/utilitie
s/pgfkeys.code.tex
\pgfkeys@pathtoks=\toks22
\pgfkeys@temptoks=\toks23

(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/pgf/utilitie
s/pgfkeysfiltered.code.tex
\pgfkeys@tmptoks=\toks24
))
\pgf@x=\dimen187
\pgf@y=\dimen188
\pgf@xa=\dimen189
\pgf@ya=\dimen190
\pgf@xb=\dimen191
\pgf@yb=\dimen192
\pgf@xc=\dimen193
\pgf@yc=\dimen194
\pgf@xd=\dimen195
\pgf@yd=\dimen196
\w@pgf@writea=\write3
\r@pgf@reada=\read2
\c@pgf@counta=\count295
\c@pgf@countb=\count296
\c@pgf@countc=\count297
\c@pgf@countd=\count298
\t@pgf@toka=\toks25
\t@pgf@tokb=\toks26
\t@pgf@tokc=\toks27
\pgf@sys@id@count=\count299

(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/pgf/systemla
yer/pgf.cfg
File: pgf.cfg 2020/12/27 v3.1.8b (3.1.8b)
)
Driver file for pgf: pgfsys-xetex.def

(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/pgf/systemla
yer/pgfsys-xetex.def
File: pgfsys-xetex.def 2020/12/27 v3.1.8b (3.1.8b)

(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/pgf/systemla
yer/pgfsys-dvipdfmx.def
File: pgfsys-dvipdfmx.def 2020/12/27 v3.1.8b (3.1.8b)

(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/pgf/systemla
yer/pgfsys-common-pdf.def
File: pgfsys-common-pdf.def 2020/12/27 v3.1.8b (3.1.8b)
)
\pgfsys@objnum=\count300
)))
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/pgf/systemla
yer/pgfsyssoftpath.code.tex
File: pgfsyssoftpath.code.tex 2020/12/27 v3.1.8b (3.1.8b)
\pgfsyssoftpath@smallbuffer@items=\count301
\pgfsyssoftpath@bigbuffer@items=\count302
)
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/pgf/systemla
yer/pgfsysprotocol.code.tex
File: pgfsysprotocol.code.tex 2020/12/27 v3.1.8b (3.1.8b)
))
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/xcolor/xcolor.
sty
Package: xcolor 2016/05/11 v2.12 LaTeX color extensions (UK)

(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/graphics-cfg/c
olor.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: xetex.def on input line 225.
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1348.
Package xcolor Info: Model `RGB' extended on input line 1364.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1366.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1367.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1368.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1370.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1371.
)
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/pgf/basiclay
er/pgfcore.code.tex
Package: pgfcore 2020/12/27 v3.1.8b (3.1.8b)

(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/pgf/math/pgf
math.code.tex
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/pgf/math/pgf
mathcalc.code.tex
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/pgf/math/pgf
mathutil.code.tex)
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/pgf/math/pgf
mathparser.code.tex
\pgfmath@dimen=\dimen197
\pgfmath@count=\count303
\pgfmath@box=\box53
\pgfmath@toks=\toks28
\pgfmath@stack@operand=\toks29
\pgfmath@stack@operation=\toks30
)
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/pgf/math/pgf
mathfunctions.code.tex
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/pgf/math/pgf
mathfunctions.basic.code.tex)
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/pgf/math/pgf
mathfunctions.trigonometric.code.tex)
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/pgf/math/pgf
mathfunctions.random.code.tex)
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/pgf/math/pgf
mathfunctions.comparison.code.tex)
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/pgf/math/pgf
mathfunctions.base.code.tex)
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/pgf/math/pgf
mathfunctions.round.code.tex)
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/pgf/math/pgf
mathfunctions.misc.code.tex)
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/pgf/math/pgf
mathfunctions.integerarithmetics.code.tex)))
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/pgf/math/pgf
mathfloat.code.tex
\c@pgfmathroundto@lastzeros=\count304
))
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/pgf/math/pgf
int.code.tex)
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/pgf/basiclay
er/pgfcorepoints.code.tex
File: pgfcorepoints.code.tex 2020/12/27 v3.1.8b (3.1.8b)
\pgf@picminx=\dimen198
\pgf@picmaxx=\dimen199
\pgf@picminy=\dimen256
\pgf@picmaxy=\dimen257
\pgf@pathminx=\dimen258
\pgf@pathmaxx=\dimen259
\pgf@pathminy=\dimen260
\pgf@pathmaxy=\dimen261
\pgf@xx=\dimen262
\pgf@xy=\dimen263
\pgf@yx=\dimen264
\pgf@yy=\dimen265
\pgf@zx=\dimen266
\pgf@zy=\dimen267
)
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/pgf/basiclay
er/pgfcorepathconstruct.code.tex
File: pgfcorepathconstruct.code.tex 2020/12/27 v3.1.8b (3.1.8b)
\pgf@path@lastx=\dimen268
\pgf@path@lasty=\dimen269
)
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/pgf/basiclay
er/pgfcorepathusage.code.tex
File: pgfcorepathusage.code.tex 2020/12/27 v3.1.8b (3.1.8b)
\pgf@shorten@end@additional=\dimen270
\pgf@shorten@start@additional=\dimen271
)
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/pgf/basiclay
er/pgfcorescopes.code.tex
File: pgfcorescopes.code.tex 2020/12/27 v3.1.8b (3.1.8b)
\pgfpic=\box54
\pgf@hbox=\box55
\pgf@layerbox@main=\box56
\pgf@picture@serial@count=\count305
)
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/pgf/basiclay
er/pgfcoregraphicstate.code.tex
File: pgfcoregraphicstate.code.tex 2020/12/27 v3.1.8b (3.1.8b)
\pgflinewidth=\dimen272
)
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/pgf/basiclay
er/pgfcoretransformations.code.tex
File: pgfcoretransformations.code.tex 2020/12/27 v3.1.8b (3.1.8b)
\pgf@pt@x=\dimen273
\pgf@pt@y=\dimen274
\pgf@pt@temp=\dimen275
)
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/pgf/basiclay
er/pgfcorequick.code.tex
File: pgfcorequick.code.tex 2020/12/27 v3.1.8b (3.1.8b)
)
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/pgf/basiclay
er/pgfcoreobjects.code.tex
File: pgfcoreobjects.code.tex 2020/12/27 v3.1.8b (3.1.8b)
)
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/pgf/basiclay
er/pgfcorepathprocessing.code.tex
File: pgfcorepathprocessing.code.tex 2020/12/27 v3.1.8b (3.1.8b)
)
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/pgf/basiclay
er/pgfcorearrows.code.tex
File: pgfcorearrows.code.tex 2020/12/27 v3.1.8b (3.1.8b)
\pgfarrowsep=\dimen276
)
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/pgf/basiclay
er/pgfcoreshade.code.tex
File: pgfcoreshade.code.tex 2020/12/27 v3.1.8b (3.1.8b)
\pgf@max=\dimen277
\pgf@sys@shading@range@num=\count306
\pgf@shadingcount=\count307
)
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/pgf/basiclay
er/pgfcoreimage.code.tex
File: pgfcoreimage.code.tex 2020/12/27 v3.1.8b (3.1.8b)

(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/pgf/basiclay
er/pgfcoreexternal.code.tex
File: pgfcoreexternal.code.tex 2020/12/27 v3.1.8b (3.1.8b)
\pgfexternal@startupbox=\box57
))
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/pgf/basiclay
er/pgfcorelayers.code.tex
File: pgfcorelayers.code.tex 2020/12/27 v3.1.8b (3.1.8b)
)
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/pgf/basiclay
er/pgfcoretransparency.code.tex
File: pgfcoretransparency.code.tex 2020/12/27 v3.1.8b (3.1.8b)
)
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/pgf/basiclay
er/pgfcorepatterns.code.tex
File: pgfcorepatterns.code.tex 2020/12/27 v3.1.8b (3.1.8b)
)
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/pgf/basiclay
er/pgfcorerdf.code.tex
File: pgfcorerdf.code.tex 2020/12/27 v3.1.8b (3.1.8b)
)))
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/pgf/modules/
pgfmoduleshapes.code.tex
File: pgfmoduleshapes.code.tex 2020/12/27 v3.1.8b (3.1.8b)
\pgfnodeparttextbox=\box58
)
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/pgf/modules/
pgfmoduleplot.code.tex
File: pgfmoduleplot.code.tex 2020/12/27 v3.1.8b (3.1.8b)
)
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/pgf/compatibil
ity/pgfcomp-version-0-65.sty
Package: pgfcomp-version-0-65 2020/12/27 v3.1.8b (3.1.8b)
\pgf@nodesepstart=\dimen278
\pgf@nodesepend=\dimen279
)
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/pgf/compatibil
ity/pgfcomp-version-1-18.sty
Package: pgfcomp-version-1-18 2020/12/27 v3.1.8b (3.1.8b)
))
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/pgf/utilities/
pgffor.sty
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/pgf/utilities/
pgfkeys.sty
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/pgf/utilitie
s/pgfkeys.code.tex))
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/pgf/math/pgfma
th.sty
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/pgf/math/pgf
math.code.tex))
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/pgf/utilitie
s/pgffor.code.tex
Package: pgffor 2020/12/27 v3.1.8b (3.1.8b)

(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/pgf/math/pgf
math.code.tex)
\pgffor@iter=\dimen280
\pgffor@skip=\dimen281
\pgffor@stack=\toks31
\pgffor@toks=\toks32
))
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/pgf/frontend
layer/tikz/tikz.code.tex
Package: tikz 2020/12/27 v3.1.8b (3.1.8b)

(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/pgf/librarie
s/pgflibraryplothandlers.code.tex
File: pgflibraryplothandlers.code.tex 2020/12/27 v3.1.8b (3.1.8b)
\pgf@plot@mark@count=\count308
\pgfplotmarksize=\dimen282
)
\tikz@lastx=\dimen283
\tikz@lasty=\dimen284
\tikz@lastxsaved=\dimen285
\tikz@lastysaved=\dimen286
\tikz@lastmovetox=\dimen287
\tikz@lastmovetoy=\dimen288
\tikzleveldistance=\dimen289
\tikzsiblingdistance=\dimen290
\tikz@figbox=\box59
\tikz@figbox@bg=\box60
\tikz@tempbox=\box61
\tikz@tempbox@bg=\box62
\tikztreelevel=\count309
\tikznumberofchildren=\count310
\tikznumberofcurrentchild=\count311
\tikz@fig@count=\count312

(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/pgf/modules/
pgfmodulematrix.code.tex
File: pgfmodulematrix.code.tex 2020/12/27 v3.1.8b (3.1.8b)
\pgfmatrixcurrentrow=\count313
\pgfmatrixcurrentcolumn=\count314
\pgf@matrix@numberofcolumns=\count315
)
\tikz@expandcount=\count316

(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/pgf/frontend
layer/tikz/libraries/tikzlibrarytopaths.code.tex
File: tikzlibrarytopaths.code.tex 2020/12/27 v3.1.8b (3.1.8b)
)))
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/geometry/geome
try.sty
Package: geometry 2020/01/02 v5.9 Page Geometry

(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/iftex/ifvtex
.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.

(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/iftex/iftex.
sty
Package: iftex 2020/03/06 v1.0d TeX engine tests
))
\Gm@cnth=\count317
\Gm@cntv=\count318
\c@Gm@tempcnt=\count319
\Gm@bindingoffset=\dimen291
\Gm@wd@mp=\dimen292
\Gm@odd@mp=\dimen293
\Gm@even@mp=\dimen294
\Gm@layoutwidth=\dimen295
\Gm@layoutheight=\dimen296
\Gm@layouthoffset=\dimen297
\Gm@layoutvoffset=\dimen298
\Gm@dimlist=\toks33
)
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/hyperref/hyper
ref.sty
Package: hyperref 2021-02-27 v7.00k Hypertext links for LaTeX

(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/ltxcmds/ltxc
mds.sty
Package: ltxcmds 2020-05-10 v1.25 LaTeX kernel commands for general use (HO)
)
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/pdftexcmds/p
dftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO
)

(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/infwarerr/in
fwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode not found.
)
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/kvsetkeys/kv
setkeys.sty
Package: kvsetkeys 2019/12/15 v1.18 Key value parser (HO)
)
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/kvdefinekeys
/kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
)
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/pdfescape/pd
fescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)
)
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/hycolor/hycolo
r.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
)
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/letltxmacro/le
tltxmacro.sty
Package: letltxmacro 2019/12/03 v1.6 Let assignment for LaTeX macros (HO)
)
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/auxhook/auxhoo
k.sty
Package: auxhook 2019-12-17 v1.6 Hooks for auxiliary files (HO)
)
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/kvoptions/kvop
tions.sty
Package: kvoptions 2020-10-07 v3.14 Key value format for package options (HO)
)
\@linkdim=\dimen299
\Hy@linkcounter=\count320
\Hy@pagecounter=\count321

(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/hyperref/pd1en
c.def
File: pd1enc.def 2021-02-27 v7.00k Hyperref: PDFDocEncoding definition (HO)
)
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/hyperref/hyper
ref-langpatches.def
File: hyperref-langpatches.def 2021-02-27 v7.00k Hyperref: patches for babel la
nguages
)
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/intcalc/intc
alc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/etexcmds/ete
xcmds.sty
Package: etexcmds 2019/12/15 v1.7 Avoid name clashes with e-TeX commands (HO)
)
\Hy@SavedSpaceFactor=\count322

(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/hyperref/puenc
.def
File: puenc.def 2021-02-27 v7.00k Hyperref: PDF Unicode definition (HO)
)
Package hyperref Info: Option `unicode' set `true' on input line 4073.
Package hyperref Info: Hyper figures OFF on input line 4192.
Package hyperref Info: Link nesting OFF on input line 4197.
Package hyperref Info: Hyper index ON on input line 4200.
Package hyperref Info: Plain pages OFF on input line 4207.
Package hyperref Info: Backreferencing OFF on input line 4212.
Package hyperref Info: Implicit mode ON; LaTeX internals redefined.
Package hyperref Info: Bookmarks ON on input line 4445.
\c@Hy@tempcnt=\count323

(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/url/url.sty
\Urlmuskip=\muskip18
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4804.
\XeTeXLinkMargin=\dimen300

(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/bitset/bitse
t.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)

(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/bigintcalc/b
igintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO
)
))
\Fld@menulength=\count324
\Field@Width=\dimen301
\Fld@charsize=\dimen302
Package hyperref Info: Hyper figures OFF on input line 6075.
Package hyperref Info: Link nesting OFF on input line 6080.
Package hyperref Info: Hyper index ON on input line 6083.
Package hyperref Info: backreferencing OFF on input line 6090.
Package hyperref Info: Link coloring OFF on input line 6095.
Package hyperref Info: Link coloring with OCG OFF on input line 6100.
Package hyperref Info: PDF/A mode OFF on input line 6105.
LaTeX Info: Redefining \ref on input line 6145.
LaTeX Info: Redefining \pageref on input line 6149.

(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/base/atbegshi-
ltx.sty
Package: atbegshi-ltx 2020/08/17 v1.0a Emulation of the original atbegshi packa
ge
with kernel methods
)
\Hy@abspage=\count325
\c@Item=\count326
\c@Hfootnote=\count327
)
Package hyperref Info: Driver (autodetected): hxetex.

(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/hyperref/hxete
x.def
File: hxetex.def 2021-02-27 v7.00k Hyperref driver for XeTeX

(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/stringenc/st
ringenc.sty
Package: stringenc 2019/11/29 v1.12 Convert strings between diff. encodings (HO
)
)
\pdfm@box=\box63
\c@Hy@AnnotLevel=\count328
\HyField@AnnotCount=\count329
\Fld@listcount=\count330
\c@bookmark@seq@number=\count331

(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/rerunfilecheck
/rerunfilecheck.sty
Package: rerunfilecheck 2019/12/05 v1.9 Rerun checks for auxiliary files (HO)

(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/base/atveryend
-ltx.sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atvery packag
e
with kernel methods
)
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/uniquecounte
r/uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 2
86.
)
\Hy@SectionHShift=\skip60
)
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/caption/subcap
tion.sty
Package: subcaption 2020/10/07 v1.3j Sub-captions (AR)

(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/caption/captio
n.sty
Package: caption 2020/10/26 v3.5g Customizing captions (AR)

(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/caption/captio
n3.sty
Package: caption3 2020/10/21 v2.2e caption3 kernel (AR)
\captionmargin=\dimen303
\captionmargin@=\dimen304
\captionwidth=\dimen305
\caption@tempdima=\dimen306
\caption@indent=\dimen307
\caption@parindent=\dimen308
\caption@hangindent=\dimen309
Package caption Info: Standard document class detected.
)
\c@caption@flags=\count332
\c@continuedfloat=\count333
Package caption Info: hyperref package is loaded.
)
\c@subfigure=\count334
\c@subtable=\count335
) (./seminar.aux)
\openout1 = `seminar.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 14.
LaTeX Font Info:    ... okay on input line 14.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 14.
LaTeX Font Info:    ... okay on input line 14.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 14.
LaTeX Font Info:    ... okay on input line 14.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 14.
LaTeX Font Info:    ... okay on input line 14.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 14.
LaTeX Font Info:    ... okay on input line 14.
LaTeX Font Info:    Checking defaults for TU/lmr/m/n on input line 14.
LaTeX Font Info:    ... okay on input line 14.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 14.
LaTeX Font Info:    ... okay on input line 14.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 14.
LaTeX Font Info:    ... okay on input line 14.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 14.
LaTeX Font Info:    ... okay on input line 14.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 14.
LaTeX Font Info:    ... okay on input line 14.
 ABD: EverySelectfont initializing macros
LaTeX Info: Redefining \selectfont on input line 14.

Package fontspec Info: Adjusting the maths setup (use [no-math] to avoid
(fontspec)             this).

\symlegacymaths=\mathgroup6
LaTeX Font Info:    Overwriting symbol font `legacymaths' in version `bold'
(Font)                  OT1/cmr/m/n --> OT1/cmr/bx/n on input line 14.
LaTeX Font Info:    Redeclaring math accent \acute on input line 14.
LaTeX Font Info:    Redeclaring math accent \grave on input line 14.
LaTeX Font Info:    Redeclaring math accent \ddot on input line 14.
LaTeX Font Info:    Redeclaring math accent \tilde on input line 14.
LaTeX Font Info:    Redeclaring math accent \bar on input line 14.
LaTeX Font Info:    Redeclaring math accent \breve on input line 14.
LaTeX Font Info:    Redeclaring math accent \check on input line 14.
LaTeX Font Info:    Redeclaring math accent \hat on input line 14.
LaTeX Font Info:    Redeclaring math accent \dot on input line 14.
LaTeX Font Info:    Redeclaring math accent \mathring on input line 14.
LaTeX Font Info:    Redeclaring math symbol \Gamma on input line 14.
LaTeX Font Info:    Redeclaring math symbol \Delta on input line 14.
LaTeX Font Info:    Redeclaring math symbol \Theta on input line 14.
LaTeX Font Info:    Redeclaring math symbol \Lambda on input line 14.
LaTeX Font Info:    Redeclaring math symbol \Xi on input line 14.
LaTeX Font Info:    Redeclaring math symbol \Pi on input line 14.
LaTeX Font Info:    Redeclaring math symbol \Sigma on input line 14.
LaTeX Font Info:    Redeclaring math symbol \Upsilon on input line 14.
LaTeX Font Info:    Redeclaring math symbol \Phi on input line 14.
LaTeX Font Info:    Redeclaring math symbol \Psi on input line 14.
LaTeX Font Info:    Redeclaring math symbol \Omega on input line 14.
LaTeX Font Info:    Redeclaring math symbol \mathdollar on input line 14.
LaTeX Font Info:    Redeclaring symbol font `operators' on input line 14.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `normal' on input line 14.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> TU/lmr/m/n on input line 14.
LaTeX Font Info:    Encoding `OT1' has changed to `TU' for symbol font
(Font)              `operators' in the math version `bold' on input line 14.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> TU/lmr/m/n on input line 14.
LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  TU/lmr/m/n --> TU/lmr/m/n on input line 14.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/cmr/m/it --> TU/lmr/m/it on input line 14.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/cmr/bx/n --> TU/lmr/b/n on input line 14.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  OT1/cmss/m/n --> TU/lmss/m/n on input line 14.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `normal'
(Font)                  OT1/cmtt/m/n --> TU/lmtt/m/n on input line 14.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  TU/lmr/m/n --> TU/lmr/b/n on input line 14.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/cmr/bx/it --> TU/lmr/b/it on input line 14.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/cmss/bx/n --> TU/lmss/b/n on input line 14.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/cmtt/m/n --> TU/lmtt/b/n on input line 14.

*geometry* driver: auto-detecting
*geometry* detected driver: xetex
*geometry* verbose mode - [ preamble ] result:
* driver: xetex
* paper: a4paper
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: 
* h-part:(L,W,R)=(71.13188pt, 455.24411pt, 71.13188pt)
* v-part:(T,H,B)=(71.13188pt, 702.78308pt, 71.13188pt)
* \paperwidth=597.50787pt
* \paperheight=845.04684pt
* \textwidth=455.24411pt
* \textheight=702.78308pt
* \oddsidemargin=-1.1381pt
* \evensidemargin=-1.1381pt
* \topmargin=-38.1381pt
* \headheight=12.0pt
* \headsep=25.0pt
* \topskip=12.0pt
* \footskip=30.0pt
* \marginparwidth=44.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.8pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

Package hyperref Info: Link coloring OFF on input line 14.

(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/hyperref/namer
ef.sty
Package: nameref 2021-04-02 v2.47 Cross-referencing by name of section

(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/refcount/refco
unt.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
)
(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/generic/gettitlestri
ng/gettitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)
)
\c@section@level=\count336
)
LaTeX Info: Redefining \ref on input line 14.
LaTeX Info: Redefining \pageref on input line 14.
LaTeX Info: Redefining \nameref on input line 14.
 (./seminar.out) (./seminar.out)
\@outlinefile=\write4
\openout4 = `seminar.out'.

Package caption Info: Begin \AtBeginDocument code.
Package caption Info: End \AtBeginDocument code.
 (./seminar.toc
LaTeX Font Info:    Trying to load font information for U+msa on input line 3.

(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/amsfonts/umsa.
fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 3.

(d:/tools/textstuidio/textlive/texlive/2021/texmf-dist/tex/latex/amsfonts/umsb.
fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
))
\tf@toc=\write5
\openout5 = `seminar.toc'.

 [1

]

Package fontspec Warning: Font "FandolFang-Regular" does not contain requested
(fontspec)                Script "CJK".

LaTeX Font Info:    Font shape `TU/lmtt/bx/n' in size <12> not available
(Font)              Font shape `TU/lmtt/b/n' tried instead on input line 37.

Package fontspec Info: Font family 'FandolFang-Regular(0)' created for font
(fontspec)             'FandolFang-Regular' with options
(fontspec)             [Script={CJK},Extension={.otf}].
(fontspec)              
(fontspec)              This font family consists of the following NFSS
(fontspec)             series/shapes:
(fontspec)              
(fontspec)             - 'normal' (m/n) with NFSS spec.:
(fontspec)             <->"[FandolFang-Regular.otf]/OT:language=dflt;"
(fontspec)             - 'small caps'  (m/sc) with NFSS spec.: 


Overfull \hbox (10.9561pt too wide) in paragraph at lines 50--51
[][]$[][][][][] [] [] [] [][][][] [] [][][][][][][][] [] [][][] [] [][][][][][]
[][][] [] [][][][][][][][][][][][][][][][][][][][][][][][][][][][][][][][] [] [
][] []
 []

[2]
File: miniconda-1.png Graphic file (type bmp)
<miniconda-1.png>
File: miniconda-2.png Graphic file (type bmp)
<miniconda-2.png>
File: miniconda-3.png Graphic file (type bmp)
<miniconda-3.png>

Overfull \hbox (21.39586pt too wide) in paragraph at lines 92--93
[][][]\TU/lmtt/m/n/12 ;<minicondaPath>;<minicondaPath>\Scripts;<minicondaPath>\
Library\bin 
 []

[3]
File: path-1.png Graphic file (type bmp)
<path-1.png>
File: path-2.png Graphic file (type bmp)
<path-2.png>
 [4] [5]
File: workspace.png Graphic file (type bmp)
<workspace.png>
File: interpreter.png Graphic file (type bmp)
<interpreter.png>
 [6] (./seminar.aux)
Package rerunfilecheck Info: File `seminar.out' has not changed.
(rerunfilecheck)             Checksum: 758CC55EE2DCB578C1068F257CA11629;881.
 ) 
Here is how much of TeX's memory you used:
 26215 strings out of 476919
 565809 string characters out of 5821393
 877269 words of memory out of 5000000
 45734 multiletter control sequences out of 15000+600000
 411935 words of font info for 92 fonts, out of 8000000 for 9000
 1348 hyphenation exceptions out of 8191
 101i,8n,104p,418b,572s stack positions out of 5000i,500n,10000p,200000b,80000s

Output written on seminar.pdf (6 pages).
