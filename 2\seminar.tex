\documentclass[12pt]{article}
\usepackage{ctex} % 中文支持
\usepackage{amsmath, amssymb}
\usepackage{graphicx}
\usepackage{tikz} % 用于画流程图
\usepackage{geometry}
\geometry{a4paper, margin=2.5cm}
\usepackage{hyperref}
\usepackage{subcaption}
\usepackage{graphicx}

\begin{document}
	
	\title{代数基础和 Python 基本数据结构}
	\author{TYTY}
	\date{2025.7.27}
	\maketitle
	
	\tableofcontents
	
	\section{代数基础}
	
	\subsection{向量点积}
	
	在高中阶段，我们学习过二维和三维向量的点积（又称内积），其定义为：
	
	\begin{equation}
		\vec{a} \cdot \vec{b} = |\vec{a}| \cdot |\vec{b}| \cdot \cos\theta
	\end{equation}
	
	其中 $\theta$ 是两个向量之间的夹角。将其推广到 $n$ 维空间中，向量 $\vec{a}, \vec{b} \in \mathbb{R}^n$，则其点积定义为：
	
	\begin{equation}
		\vec{a} \cdot \vec{b} = \sum_{i=1}^{n} a_i b_i
	\end{equation}
	deepseek
	这是一种代数操作，不再需要计算夹角。例如，对于两个三维向量 $\vec{a} = (1, 2, 3)$，$\vec{b} = (4, 5, 6)$：
	
	\[
	\vec{a} \cdot \vec{b} = 1 \cdot 4 + 2 \cdot 5 + 3 \cdot 6 = 4 + 10 + 18 = 32
	\]
	
	点积的几何意义是：若 $\vec{a}$ 与 $\vec{b}$ 同方向，则点积为最大值；若它们垂直，则点积为 0；若方向相反，则点积为负值。
	
	点积还可以用于投影计算、判断两个向量是否正交（点积为 0 即正交）等，在机器学习中也广泛应用于计算向量之间的相似性。
	
	\subsection{矩阵}
	
	矩阵（Matrix）的概念最早可追溯到中国《九章算术》，西方则是在 19 世纪由英国数学家凯莱（Cayley）等人正式发展起来。它是代数中的一种重要结构，用于表示线性变换与方程组。下面我们将通过线性方程组介绍矩阵，并从线性方程组出发定义矩阵的运算。
	
	\subsubsection{线性方程组与矩阵表示}
	
	考虑如下线性方程组：
	
	
	\begin{equation}
		\left\{
		\begin{aligned}
			2x + y &= 4 \\
			x + 2y &= 5
		\end{aligned}
		\right.
		\label{eqasys}
	\end{equation}

	
	我们可以将其表示为矩阵乘法形式：
	
	\[
	\begin{bmatrix}
		2 & 1 \\
		1 & 2 \\
	\end{bmatrix}
	\begin{bmatrix}
		x \\
		y
	\end{bmatrix}
	=
	\begin{bmatrix}
		4 \\
		5
	\end{bmatrix}
	\]
	
	这三个部分分别为：
	
	\begin{itemize}
		\item 系数矩阵 $A = \begin{bmatrix} 2 & 1 \\ 1 & 2 \end{bmatrix}$
		\item 变量 $\vec{x} = \begin{bmatrix} x \\ y \end{bmatrix}$
		\item 常数向量 $\vec{b} = \begin{bmatrix} 4 \\ 5 \end{bmatrix}$
	\end{itemize}
	

	
	\subsubsection{矩阵与列向量}
	
	我们从向量点积的角度重新书写线性方程~\eqref{eqasys}。每一个方程实际上都可以看作是一个行向量与变量列向量的点积。例如第一个方程 $2x + y = 4$，可以表示为：
	
	\[
	[2\quad 1] \cdot \begin{bmatrix} x \\ y \end{bmatrix} = 4
	\]
	
	第二个方程同理为：
	
	\[
	[1\quad 2] \cdot \begin{bmatrix} x \\ y \end{bmatrix} = 5
	\]
	
	综合之前方程组的矩阵表示形式，如果我们要定义 
	$\begin{bmatrix} 2 & 1 \\ 1 & 2 \end{bmatrix}$ 与 
	$\begin{bmatrix} x \\ y \end{bmatrix}$ 
	之间的乘法，那么一个很自然的定义是：矩阵的每一行分别与列向量$\begin{bmatrix} x \\ y \end{bmatrix}$进行点积，得到的结果组成一个新的列向量：
	
	\begin{equation}
		\begin{bmatrix}
			a_{11} & a_{12} \\
			a_{21} & a_{22}
		\end{bmatrix}
		\begin{bmatrix}
			x_1 \\
			x_2
		\end{bmatrix}
		=
		\begin{bmatrix}
			a_{11}x_1 + a_{12}x_2 \\
			a_{21}x_1 + a_{22}x_2
		\end{bmatrix}
		\label{eq:matrix_vector_product}
	\end{equation}
	
	\subsubsection{矩阵与矩阵}
	
	如果变量向量 $\vec{x}$ 不止一列，而有多列
	\[
	X = \begin{bmatrix}
		x_{11} & x_{12} & \cdots & x_{1m} \\
		x_{21} & x_{22} & \cdots & x_{2m}
	\end{bmatrix},
	\]
	我们可以将每一列看作一个单独的列向量
	\[
	X = \big[ \vec{x}_1 \quad \vec{x}_2 \quad \cdots \quad \vec{x}_m \big],
	\]
	，然后对矩阵 $A$ 与每列分别进行式~\ref{eq:matrix_vector_product}同样的运算，最后将结果拼接成一个新的矩阵。形式上，可以写成：
	
	\[
	A X = \big[ A \vec{x}_1 \quad A \vec{x}_2 \quad \cdots \quad A \vec{x}_m \big],
	\]
	
	其中 $\vec{x}_j$ 是 $X$ 的第 $j$ 列。
	
	因此：
	\[
	A =
	\begin{bmatrix}
		a_{11} & a_{12} \\
		a_{21} & a_{22}
	\end{bmatrix}
	\quad \in \mathbb{R}^{2 \times 2},
	\quad
	B =
	\begin{bmatrix}
		b_{11} & b_{12} & \cdots & b_{1m} \\
		b_{21} & b_{22} & \cdots & b_{2m}
	\end{bmatrix}
	\quad \in \mathbb{R}^{2 \times m},
	\]
	之间的乘积可以定义为
	
	\begin{equation}
		AB =
		\begin{bmatrix}
			a_{11}b_{11} + a_{12}b_{21} & a_{11}b_{12} + a_{12}b_{22} & \cdots & a_{11}b_{1m} + a_{12}b_{2m} \\
			a_{21}b_{11} + a_{22}b_{21} & a_{21}b_{12} + a_{22}b_{22} & \cdots & a_{21}b_{1m} + a_{22}b_{2m}
		\end{bmatrix}.
		\label{eq:matrix_matrix_product}
	\end{equation}
	
	换句话说，矩阵与矩阵之间的乘法$A\cdot B$本质上是对矩阵 $A$对矩阵 $B$ 的每一列做式\ref{eq:matrix_vector_product}定义的运算，最后拼接构成新的矩阵。
	
	
	
	\subsection{矩阵的定义和基本运算}
	以下更严谨的陈述1.2中的内容。
	
	$R$上的$m\times n$维矩阵为：
	\begin{equation}
		A = \begin{bmatrix}
			a_{11} & a_{12} & \cdots & a_{1n} \\
			a_{21} & a_{22} & \cdots & a_{2n} \\
			\vdots & \vdots & \ddots & \vdots \\
			a_{m1} & a_{m2} & \cdots & a_{mn}
		\end{bmatrix}
	\end{equation}
	
	记为：$$A = \begin{bmatrix}
		a_{ij}
	\end{bmatrix}\in R_{m\times n} (1\leq i\leq m, 1\leq j \leq n )$$  
	其中$R_{m\times n}$表示元素属于$R$的所有$m\times n$维矩阵构成的集合。
	\paragraph{相等}
	若矩阵$A$的维度和矩阵$B$的维度相同，且每个元素也相同，则认为矩阵$A = B$
	
	\paragraph{矩阵的加法}
	
	若矩阵 $A$ 与矩阵 $B$ 的维度相同，即 $A = \begin{bmatrix}
		a_{ij}
	\end{bmatrix} \in R^{m\times n}$，$B = \begin{bmatrix}
		b_{ij}
	\end{bmatrix} \in R^{m\times n}$，则定义矩阵加法 $A + B$ 为对应元素相加所得到的矩阵：
	
	\begin{equation}
		C = A + B := \left[
		\begin{array}{cccc}
			c_{11} & c_{12} & \cdots & c_{1n} \\
			c_{21} & c_{22} & \cdots & c_{2n} \\
			\vdots & \vdots & \ddots & \vdots \\
			c_{m1} & c_{m2} & \cdots & c_{mn}
		\end{array}
		\right] \in R^{m\times n}, \quad \text{其中 } c_{ij} = a_{ij} + b_{ij}
	\end{equation}
	
	
	
	\paragraph{矩阵的乘法}
	
	对于矩阵 $A = \begin{bmatrix}
		a_{ij}
	\end{bmatrix} \in R^{m\times p}$，$B = \begin{bmatrix}
		b_{ij}
	\end{bmatrix} \in R^{p\times n}$，我们定义（记作 $:=$）它们的乘法运算：
	
	\begin{equation}
		C = A \cdot B := \left[
		\begin{array}{cccc}
			c_{11} & c_{12} & \cdots & c_{1n} \\
			c_{21} & c_{22} & \cdots & c_{2n} \\
			\vdots & \vdots & \ddots & \vdots \\
			c_{m1} & c_{m2} & \cdots & c_{mn}
		\end{array}
		\right] \in R^{m\times n}, \quad \text{其中 } c_{ij} = \sum_{k=1}^{p} a_{ik} b_{kj}
	\end{equation}
	
	
	也就是说，$C$的第$i$行第$j$列元素$c_{ij}$是$A$的第$i$行与$B$的第$j$列的点积。在这样的定义下，矩阵乘法满足如下规则：
	
	\begin{enumerate}
		\item 因为$c_{ij}$是由对应向量的点积得到的，因此对应向量必须位数相等，也即矩阵$A$的列数必须等于矩阵$B$的行数，即 $A \in R_{m\times p},\ B \in R_{p\times n}$。
		\item 因为$c_{ij}$是由$A$的第$i$行与$B$的第$j$列的点积得到，因此$C$的行数等于$A$的行数一致，$C$的列数和$B$的列数一致 $m\times n$ 维矩阵，即 $C \in R^{m\times n}$
	\end{enumerate}
	
	\paragraph{特别的：}
	
	\begin{itemize}
		\item \textbf{行列向量的矩阵乘法}：设 $x \in R^{n \times 1}$ 为列向量，$y \in R^{1 \times n}$ 为行向量，$y \cdot x \in R$其实就是原来讨论的向量的点积。
		\item \textbf{线性方程组的矩阵表示}：一个含有 $m$ 个方程、$n$ 个未知数的线性方程组一般可以表示为：
		$$
		\left\{
		\begin{aligned}
			a_{11}x_1 + a_{12}x_2 + \cdots + a_{1n}x_n &= b_1 \\
			a_{21}x_1 + a_{22}x_2 + \cdots + a_{2n}x_n &= b_2 \\
			\vdots \quad\quad\quad\quad\quad\quad\quad\quad & \vdots \\
			a_{m1}x_1 + a_{m2}x_2 + \cdots + a_{mn}x_n &= b_m
		\end{aligned}
		\right.$$
		我们可以将其写为矩阵形式：
		\[
		A x = b
		\]
		其中：
		\[
		A = \begin{bmatrix}
			a_{11} & a_{12} & \cdots & a_{1n} \\
			a_{21} & a_{22} & \cdots & a_{2n} \\
			\vdots & \vdots & \ddots & \vdots \\
			a_{m1} & a_{m2} & \cdots & a_{mn}
		\end{bmatrix} \in R^{m \times n}, \quad
		x = \begin{bmatrix}
			x_1 \\
			x_2 \\
			\vdots \\
			x_n
		\end{bmatrix} \in R^{n \times 1}, \quad
		b = \begin{bmatrix}
			b_1 \\
			b_2 \\
			\vdots \\
			b_m
		\end{bmatrix} \in R^{m \times 1}
		\]
		
		这表示每个方程的左边是矩阵 $A$ 的一行与变量列向量 $x$ 的点积，右边是对应的常数 $b_i$。矩阵表示不仅简洁，而且为后续求解提供了统一的代数结构。
		
	\end{itemize}
	
	\paragraph{注：}矩阵的乘法源于向量的点积和线性方程组；而从向量点积、线性方程组延伸出的矩阵乘法，反过来也为点积和线性方程组提供了更简洁的表达
	
	\subsection{例子}
	
	\paragraph{例 1：线性方程组的矩阵表示}
	
	请将以下线性方程组写成矩阵形式 $Ax = b$：
	
	\begin{enumerate}
		\item 
		\[
		\left\{
		\begin{aligned}
			2x_1 - x_2 + 3x_3 &= 5 \\
			4x_1 + x_2 - x_3 &= 6
		\end{aligned}
		\right.
		\]
		
		\item
		\[
		\left\{
		\begin{aligned}
			x + 2y + 3z &= 4 \\
			2x + y + z &= 5 \\
			3x + 4y + z &= 6
		\end{aligned}
		\right.
		\]
		
		\item
		\[
		\left\{
		\begin{aligned}
			x_1 - 2x_2 + x_3 - x_4 &= 0 \\
			3x_1 + x_2 - x_3 + 2x_4 &= 1
		\end{aligned}
		\right.
		\]
	\end{enumerate}
	
	\paragraph{例 2：行列向量的乘法}
	
	已知向量 $x$ 和 $y$ 如下，写出它们的维度，并计算乘积 $x \cdot y$：
	
	\begin{enumerate}
		\item 
		$x = \begin{bmatrix} 1 \\ 2 \\ 3 \end{bmatrix}$，$y = \begin{bmatrix} 4 & 5 & 6 \end{bmatrix}$
		
		$y = \begin{bmatrix} 1 \\ 2 \\ 3 \end{bmatrix}$，$x = \begin{bmatrix} 4 & 5 & 6 \end{bmatrix}$
		
		\item 
		$x = \begin{bmatrix} 1 & 2 \end{bmatrix}$，$y = \begin{bmatrix} 3 \\ 4 \end{bmatrix}$
		
		\item 
		$x = \begin{bmatrix} 2 \\ -1 \end{bmatrix}$，$y = \begin{bmatrix} 3 & 1 \end{bmatrix}$
	\end{enumerate}
	
	\subsection*{参考答案(AI生成)}
	
	\paragraph{例 1 答案：线性方程组的矩阵表示}
	
	\begin{enumerate}
		\item
		\[
		A = \begin{bmatrix}
			2 & -1 & 3 \\
			4 & 1 & -1
		\end{bmatrix}, \quad
		x = \begin{bmatrix}
			x_1 \\ x_2 \\ x_3
		\end{bmatrix}, \quad
		b = \begin{bmatrix}
			5 \\ 6
		\end{bmatrix}
		\]
		
		\item
		\[
		A = \begin{bmatrix}
			1 & 2 & 3 \\
			2 & 1 & 1 \\
			3 & 4 & 1
		\end{bmatrix}, \quad
		x = \begin{bmatrix}
			x \\ y \\ z
		\end{bmatrix}, \quad
		b = \begin{bmatrix}
			4 \\ 5 \\ 6
		\end{bmatrix}
		\]
		
		\item
		\[
		A = \begin{bmatrix}
			1 & -2 & 1 & -1 \\
			3 & 1 & -1 & 2
		\end{bmatrix}, \quad
		x = \begin{bmatrix}
			x_1 \\ x_2 \\ x_3 \\ x_4
		\end{bmatrix}, \quad
		b = \begin{bmatrix}
			0 \\ 1
		\end{bmatrix}
		\]
	\end{enumerate}
	
	\paragraph{例 2 答案：行列向量的乘法}
	
	\begin{enumerate}
		\item 
		首先：
		\[
		x = \begin{bmatrix} 1 \\ 2 \\ 3 \end{bmatrix} \in R^{3 \times 1}, \quad y = \begin{bmatrix} 4 & 5 & 6 \end{bmatrix} \in R^{1 \times 3}
		\]
		\[
		x \cdot y = \begin{bmatrix}
			1 \cdot 4 & 1 \cdot 5 & 1 \cdot 6 \\
			2 \cdot 4 & 2 \cdot 5 & 2 \cdot 6 \\
			3 \cdot 4 & 3 \cdot 5 & 3 \cdot 6
		\end{bmatrix}
		=
		\begin{bmatrix}
			4 & 5 & 6 \\
			8 & 10 & 12 \\
			12 & 15 & 18
		\end{bmatrix} \in R^{3 \times 3}
		\]
		\[
		y \cdot x = 4 \cdot 1 + 5 \cdot 2 + 6 \cdot 3 = 4 + 10 + 18 = 32 \in R
		\]
		
		\item
		\[
		x = \begin{bmatrix} 1 & 2 \end{bmatrix} \in R^{1 \times 2}, \quad y = \begin{bmatrix} 3 \\ 4 \end{bmatrix} \in R^{2 \times 1}
		\]
		\[
		x \cdot y = 1 \cdot 3 + 2 \cdot 4 = 3 + 8 = 11 \in R
		\]
		
		\item
		\[
		x = \begin{bmatrix} 2 \\ -1 \end{bmatrix} \in R^{2 \times 1}, \quad y = \begin{bmatrix} 3 & 1 \end{bmatrix} \in R^{1 \times 2}
		\]
		\[
		x \cdot y =
		\begin{bmatrix}
			2 \cdot 3 & 2 \cdot 1 \\
			-1 \cdot 3 & -1 \cdot 1
		\end{bmatrix}
		=
		\begin{bmatrix}
			6 & 2 \\
			-3 & -1
		\end{bmatrix} \in R^{2 \times 2}
		\]
	\end{enumerate}
	
	
	\subsection{通过列表实现矩阵}
	可以用嵌套列表存储矩阵，尝试在python中实现以下的矩阵乘法(不附答案，自行获取和验证)。嵌套列表的用法:
	\begin{verbatim}
		# 用嵌套列表表示矩阵
		A = [
		[1, 2, 3],
		[4, 5, 6],
		[7, 8, 9]
		]
		
		# 访问矩阵元素
		print("矩阵A的元素：")
		for i in range(len(A)):
		    for j in range(len(A[0])):
		        print(A[i][j], end=' ')
		        print()  # 换行
	\end{verbatim}
	
	
	\paragraph{例题 1：实现 $2 \times 3$ 与 $3 \times 2$ 的矩阵乘法}
	
	已知矩阵：
	\[
	A = \begin{bmatrix}
		1 & 2 & 3 \\
		4 & 5 & 6
	\end{bmatrix}, \quad
	B = \begin{bmatrix}
		7 & 8 \\
		9 & 10 \\
		11 & 12
	\end{bmatrix}
	\]
	
	请使用嵌套列表编写 Python 程序，计算 $A \cdot B$。
	
	\paragraph{例题 2：实现列向量与行向量的乘法}
	
	已知向量：
	\[
	x = \begin{bmatrix} 1 \\ 2 \\ 3 \end{bmatrix}, \quad y = \begin{bmatrix} 4 & 5 & 6 \end{bmatrix}
	\]
	
	请计算 $x \cdot y$ 和 $y \cdot x$，分别观察其结果维度与结构。
	
	\paragraph{例题 3：验证不可乘法的情况}
	
	试判断以下两个矩阵是否可以相乘，并解释原因（可以用程序报错体现）：
	
	\[
	C = \begin{bmatrix}
		1 & 0 \\
		2 & 1
	\end{bmatrix}, \quad
	D = \begin{bmatrix}
		3 & 4 & 5
	\end{bmatrix}
	\]
	
	
\section{附录}
\subsection{搜索引擎}
\begin{itemize}
	\item 微软：\url{cn.bing.com}
	\item 百度：\url{baidu.com}
	\item Googla：\url{google.com}
\end{itemize}

\subsection{商用网页大模型}
\begin{enumerate}
	\item Deepseek：\url{chat.deepseek.com}
	\item Moonshot：\url{https://www.kimi.com}
	\item 阿里：\url{https://www.tongyi.com/qianwen/}
	\item 豆包：\url{https://www.doubao.com/chat/}
	\item []------------------------------------------
	\item OpenAI：\url{https://chatgpt.com/}
	\item Anthropic：\url{https://claude.ai/}
	\item Google：\url{https://gemini.google.com/}
\end{enumerate}

\subsection{其他}
\begin{itemize}
	\item (开源)代码托管平台Github：\url{https://github.com/}
	\item 论文预印版arxiv：\url{https://arxiv.org}
	\item 学术论文检索Googla scholar：\url{https://scholar.google.com/}
	\item CS自学指南：\url{https://csdiy.wiki/}
	\item 知网(中文文献)：搜索cnki
\end{itemize}

\end{document}
