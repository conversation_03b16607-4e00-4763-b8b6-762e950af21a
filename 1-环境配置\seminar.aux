\relax 
\providecommand\hyper@newdestlabel[2]{}
\providecommand\HyperFirstAtBeginDocument{\AtBeginDocument}
\HyperFirstAtBeginDocument{\ifx\hyper@anchor\@undefined
\global\let\oldcontentsline\contentsline
\gdef\contentsline#1#2#3#4{\oldcontentsline{#1}{#2}{#3}}
\global\let\oldnewlabel\newlabel
\gdef\newlabel#1#2{\newlabelxx{#1}#2}
\gdef\newlabelxx#1#2#3#4#5#6{\oldnewlabel{#1}{{#2}{#3}}}
\AtEndDocument{\ifx\hyper@anchor\@undefined
\let\contentsline\oldcontentsline
\let\newlabel\oldnewlabel
\fi}
\fi}
\global\let\hyper@last\relax 
\gdef\HyperFirstAtBeginDocument#1{#1}
\providecommand*\HyPL@Entry[1]{}
\HyPL@Entry{0<</S/D>>}
\@writefile{toc}{\contentsline {section}{\numberline {1}环境安装}{2}{section.1}\protected@file@percent }
\@writefile{toc}{\contentsline {paragraph}{写一个 Python 程序需要什么？}{2}{section*.2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {1.1}IDE}{2}{subsection.1.1}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {1.2}环境管理工具}{2}{subsection.1.2}\protected@file@percent }
\@writefile{toc}{\contentsline {subsubsection}{\numberline {1.2.1}Miniconda 安装}{3}{subsubsection.1.2.1}\protected@file@percent }
\providecommand*\caption@xref[2]{\@setref\relax\@undefined{#1}}
\newlabel{miniconda-1}{{1a}{3}{miniconda-1\relax }{figure.caption.3}{}}
\newlabel{sub@miniconda-1}{{a}{3}{miniconda-1\relax }{figure.caption.3}{}}
\newlabel{miniconda-2}{{1b}{3}{miniconda-2\relax }{figure.caption.3}{}}
\newlabel{sub@miniconda-2}{{b}{3}{miniconda-2\relax }{figure.caption.3}{}}
\newlabel{miniconda-3}{{1c}{3}{miniconda-3\relax }{figure.caption.3}{}}
\newlabel{sub@miniconda-3}{{c}{3}{miniconda-3\relax }{figure.caption.3}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {1}{\ignorespaces 安装Miniconda\relax }}{3}{figure.caption.3}\protected@file@percent }
\newlabel{miniconda}{{1}{3}{安装Miniconda\relax }{figure.caption.3}{}}
\newlabel{path-1}{{2a}{4}{path-1\relax }{figure.caption.4}{}}
\newlabel{sub@path-1}{{a}{4}{path-1\relax }{figure.caption.4}{}}
\newlabel{path-2}{{2b}{4}{path-2\relax }{figure.caption.4}{}}
\newlabel{sub@path-2}{{b}{4}{path-2\relax }{figure.caption.4}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {2}{\ignorespaces windows11设置环境变量\relax }}{4}{figure.caption.4}\protected@file@percent }
\newlabel{syspath}{{2}{4}{windows11设置环境变量\relax }{figure.caption.4}{}}
\@writefile{toc}{\contentsline {subsection}{\numberline {1.3}为 VSCode 安装 Python 插件}{4}{subsection.1.3}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {1.4}在 VSCode 中激活 Conda 环境}{5}{subsection.1.4}\protected@file@percent }
\@writefile{toc}{\contentsline {subsection}{\numberline {1.5}运行.py 代码}{6}{subsection.1.5}\protected@file@percent }
\newlabel{workspace}{{3a}{6}{设置工作区\relax }{figure.caption.5}{}}
\newlabel{sub@workspace}{{a}{6}{设置工作区\relax }{figure.caption.5}{}}
\newlabel{interpreter}{{3b}{6}{选择解释器\relax }{figure.caption.5}{}}
\newlabel{sub@interpreter}{{b}{6}{选择解释器\relax }{figure.caption.5}{}}
\@writefile{lof}{\contentsline {figure}{\numberline {3}{\ignorespaces 设置vscode\relax }}{6}{figure.caption.5}\protected@file@percent }
\newlabel{settingvsc}{{3}{6}{设置vscode\relax }{figure.caption.5}{}}
\gdef \@abspage@last{6}
