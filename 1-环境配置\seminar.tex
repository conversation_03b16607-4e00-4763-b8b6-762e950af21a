\documentclass[12pt]{article}
\usepackage{ctex} % 中文支持
\usepackage{amsmath, amssymb}
\usepackage{graphicx}
\usepackage{tikz} % 用于画流程图
\usepackage{geometry}
\geometry{a4paper, margin=2.5cm}
\usepackage{hyperref}
\usepackage{subcaption}
\usepackage{graphicx}



\begin{document}
	\tableofcontents
	\title{2025.7 讨论班}  %大括号里填写标题
	\author{TYTY}  %大括号里填写作者姓名
	\date{2025.7.26}    %大括号里填写\today会自动生成当前的日期
	\maketitle     %我们写了以上内容以后一定要添加这个，制作标题，否则上面的内容都是无效的。


	\section{环境安装}
	\paragraph{写一个 Python 程序需要什么？}
	
	要成功编写并运行一个 Python 程序，通常需要以下几个组件：
	
	\begin{enumerate}
		\item 编辑器或 IDE：用于编写代码。
		\item Python 解释器：将.py 文件中的代码翻译为机器可以执行的指令。
		\item 第三方库或模块：如 numpy、pandas、pytorch 等，不同的项目往往需要不同版本的库。
		\item 环境管理工具（可选但推荐）：用于隔离项目环境，避免库版本冲突。
	\end{enumerate}
	\subsection{IDE}
	
	IDE（Integrated Development Environment，集成开发环境）是一种用于辅助编程的工具集合，它将代码编写、运行、调试、版本控制等功能集成在一个统一的界面中。常见的 IDE 包括 VSCode、PyCharm、Eclipse 等。
	
	本次讨论班选用 VSCode（Visual Studio Code）作为演示，它是一款轻量级且插件丰富的 IDE，支持多种语言，下载链接:\textbf{\url{https://code.visualstudio.com/}}
	

	
	\subsection{环境管理工具}
	
	随着项目的复杂性增加，不同项目往往依赖不同版本的库或 Python 本身。为了避免这些依赖之间的冲突，我们需要一种方式来\textbf{隔离不同项目的环境}。本次讨论班使用Miniconda
	\footnote{Miniconda是 Anaconda 的轻量级版本，只包含最基本的包管理工具conda.\\
		\url{https://www.anaconda.com/docs/getting-started/miniconda/main}}
	作为演示。
	
	windows系统下载链接:
	\begin{itemize}
		\item \textbf{\url{https://repo.anaconda.com/miniconda/Miniconda3-latest-Windows-x86_64.exe}}
	\end{itemize}
	
	\subsubsection{Miniconda 安装}
	如图\ref{miniconda}，(b)中可以选择miniconda安装目录(包括后续虚拟环境目录，不建议选择系统盘)，(c)不建议选择前两项，可以勾选第三项(在库安装完成后删除安装包)
	\begin{figure}[h]
		\centering
		\begin{subfigure}[b]{0.3\textwidth}
			\includegraphics[width=\textwidth]{miniconda-1.png}
			\caption{miniconda-1}
			\label{miniconda-1}
		\end{subfigure}
		\hfill
		\begin{subfigure}[b]{0.3\textwidth}
			\includegraphics[width=\textwidth]{miniconda-2.png}
			\caption{miniconda-2}
			\label{miniconda-2}
		\end{subfigure}
		\hfill
		\begin{subfigure}[b]{0.3\textwidth}
			\includegraphics[width=\textwidth]{miniconda-3.png}
			\caption{miniconda-3}
			\label{miniconda-3}
		\end{subfigure}
		\caption{安装Miniconda}
		\label{miniconda}
	\end{figure}
	
	完成上述安装之后，前往高级系统设置向系统变量添加Miniconda目录，假设Miniconda安装目录(minicondaPath)为:
	\begin{itemize}
		\item \verb|D:\miniconda|
	\end{itemize}
	\begin{itemize}
		\item \textbf{Windows 7 添加 环境变量:}
		\begin{itemize}
			\item 右键点击“计算机”图标，选择“属性”。
			\item 点击左侧“高级系统设置”。
			\item 在弹出的窗口中点击“环境变量”。
			\item 在“系统变量”区域找到变量名为 \texttt{Path}，点击“编辑”。
			\item 在文本末尾添加以下内容（用英文分号分隔，替换<>内的内容并去掉<>）：
			
			\item[]
			\verb|;<minicondaPath>;<minicondaPath>\Scripts;<minicondaPath>\Library\bin|
			
			\item \textbf{若 minocondaPath 为}\verb|D:\miniconda|，即为添加以下内容:
			
			\item[]
			\verb|;D:\miniconda;D:\miniconda\Scripts;D:\miniconda\Library\bin|
			
			\item 点击“确定”保存并退出所有窗口。
			\item 注意：Windows 7 编辑框是单行文本框，务必不要删除已有内容，只在末尾追加。
		\end{itemize}
		
		
		\item \textbf{Windows 10、11 添加 Path:}
		\begin{itemize}
			\item 点击“开始菜单”，搜索“环境变量”并打开“编辑系统环境变量”。
			\item 点击“环境变量”按钮。
			\item 在“系统变量”中选中 \texttt{Path}，点击“编辑”。
			\item 点击“新建”，依次添加(替换<>内的内容并去掉<>)：
			
			\item[]
			\verb|<minicondaPath>|
			
			\item[]
			\verb|<minicondaPath>\Scripts|
			
			\item[]
			\verb|<minicondaPath>\Library\bin|
			
			\item \textbf{若 minocondaPath 为}\verb|D:\miniconda|，即为添加以下内容:
			
			\item[]
			\verb|D:\miniconda|
			
			\item[]
			\verb|D:\miniconda\Scripts|
			
			\item[]
			\verb|D:\miniconda\Library\bin|
			
			\item 一路点击保存或确定。
		\end{itemize}
	\end{itemize}
	
	\begin{figure}[h]
		\centering
		\begin{subfigure}[b]{0.4\textwidth}
			\includegraphics[width=\textwidth]{path-1.png}
			\caption{path-1}
			\label{path-1}
		\end{subfigure}
		\hfill
		\begin{subfigure}[b]{0.4\textwidth}
			\includegraphics[width=\textwidth]{path-2.png}
			\caption{path-2}
			\label{path-2}
		\end{subfigure}
		\hfill
		\caption{windows11设置环境变量}
		\label{syspath}
	\end{figure}
	
	\subsection{为 VSCode 安装 Python 插件}
	在使用vscode前，可\textbf{安装中文界面：}
	\begin{enumerate}
		\item 打开 VSCode，点击左侧扩展图标（四个方块组成的图标）
		\item 在搜索栏输入"Chinese"，点击install安装，可以参考其详情页面\footnote{通过使用“Configure Display Language”命令显式设置 VS Code 显示语言，可以替代默认 UI 语言。 按下“Ctrl+Shift+P”组合键以显示“命令面板”，然后键入“display”以筛选并显示“Configure Display Language”命令。按“Enter”，然后会按区域设置显示安装的语言列表，并突出显示当前语言设置。选择另一个“语言”以切换 UI 语言。 请参阅文档并获取更多信息。}进行使用。
	\end{enumerate}
	也可以\textbf{安装其他颜色、主题}，本人使用的浅色主题"bluloco"，其他颜色主题可以去网上搜索。
	
	
	为了在 VSCode 中方便地进行 Python 开发，建议安装官方的 Python 插件。安装步骤如下：
	
	\begin{enumerate}
		\item 打开 VSCode，点击左侧扩展图标（四个方块组成的图标）。
		\item 在搜索栏输入“Python”，找到由 Microsoft 发布的插件，名称为“Python”。
		\item 点击“安装”按钮，完成插件安装。
		\item 安装完成后，插件会自动启用，并提供代码高亮、智能提示、调试支持等功能。
	\end{enumerate}
	
	安装该插件后，建议进一步搜索需要安装 Jupyter 插件，方便后续数据分析任务.
	
	\vspace{0.5em}
	
	\subsection{在 VSCode 中激活 Conda 环境}
	
	使用 Conda 创建虚拟环境后，为了在 VSCode 中正确使用该环境，需要完成 Conda 的初始化并激活对应环境。具体步骤如下：
	
	\begin{enumerate}
		\item 在 CMD（命令提示符）或 PowerShell 中执行初始化命令，确保 Conda shell 集成正常。\footnote{CMD：win+r 输入 \texttt{cmd} 并回车；PowerShell：vscode 中按 \texttt{Ctrl + Shift + \`{}}}
		
		\begin{verbatim}
			conda init
		\end{verbatim}
		该命令会配置你的 shell，使你能直接使用 \texttt{conda activate} 命令。
		
		\item 若尚未创建环境，可执行（将 <name> 替换为你的虚拟环境名称）：
		
		\begin{verbatim}
			conda create -n <name> python=3.11
		\end{verbatim}
		
		\item 关闭并重新打开命令行窗口（CMD、PowerShell），激活环境命令如下：
		
		\begin{verbatim}
			conda activate <name>
		\end{verbatim}
		
		激活后，命令行提示符会显示环境名称，如：
		
		\begin{verbatim}
			(<name>) D:\miniconda\envs\<name>
		\end{verbatim}
	\end{enumerate}
	
	
	\subsection{运行.py 代码}
	在激活conda环境之后
	\begin{enumerate}
		\item 选择一个文件夹作为工作区
		\item 在工作区新建一个<filename>.py文件(带有.py后缀)
		\item 点开.py文件以编辑.py文件，如：print('helloworld')
		\item 点击右下角interpreter，顶部弹出框中选择刚刚安装的虚拟环境
		\item 点击右上角 “播放” 键运行。
	\end{enumerate}
	
	\begin{figure}[h]
		\centering
		\begin{subfigure}[b]{0.7\textwidth}
			\includegraphics[width=\textwidth]{workspace.png}
			\caption{设置工作区}
			\label{workspace}
		\end{subfigure}
		\hfill
		\begin{subfigure}[b]{0.7\textwidth}
			\includegraphics[width=\textwidth]{interpreter.png}
			\caption{选择解释器}
			\label{interpreter}
		\end{subfigure}
		\hfill
		\caption{设置vscode}
		\label{settingvsc}
	\end{figure}

	
\end{document}