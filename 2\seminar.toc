\contentsline {section}{\numberline {1}代数基础}{1}{section.1}%
\contentsline {subsection}{\numberline {1.1}向量点积}{1}{subsection.1.1}%
\contentsline {subsection}{\numberline {1.2}矩阵}{2}{subsection.1.2}%
\contentsline {subsubsection}{\numberline {1.2.1}线性方程组与矩阵表示}{2}{subsubsection.1.2.1}%
\contentsline {subsubsection}{\numberline {1.2.2}矩阵与列向量}{3}{subsubsection.1.2.2}%
\contentsline {subsubsection}{\numberline {1.2.3}矩阵与矩阵}{3}{subsubsection.1.2.3}%
\contentsline {subsection}{\numberline {1.3}矩阵的定义和基本运算}{4}{subsection.1.3}%
\contentsline {paragraph}{相等}{4}{section*.2}%
\contentsline {paragraph}{矩阵的加法}{4}{section*.3}%
\contentsline {paragraph}{矩阵的乘法}{4}{section*.4}%
\contentsline {paragraph}{特别的：}{5}{section*.5}%
\contentsline {paragraph}{注：}{5}{section*.6}%
\contentsline {subsection}{\numberline {1.4}例子}{5}{subsection.1.4}%
\contentsline {paragraph}{例 1：线性方程组的矩阵表示}{5}{section*.7}%
\contentsline {paragraph}{例 2：行列向量的乘法}{6}{section*.8}%
\contentsline {paragraph}{例 1 答案：线性方程组的矩阵表示}{6}{section*.10}%
\contentsline {paragraph}{例 2 答案：行列向量的乘法}{7}{section*.11}%
\contentsline {subsection}{\numberline {1.5}通过列表实现矩阵}{7}{subsection.1.5}%
\contentsline {paragraph}{例题 1：实现 $2 \times 3$ 与 $3 \times 2$ 的矩阵乘法}{8}{section*.12}%
\contentsline {paragraph}{例题 2：实现列向量与行向量的乘法}{8}{section*.13}%
\contentsline {paragraph}{例题 3：验证不可乘法的情况}{8}{section*.14}%
\contentsline {section}{\numberline {2}附录}{8}{section.2}%
\contentsline {subsection}{\numberline {2.1}搜索引擎}{8}{subsection.2.1}%
\contentsline {subsection}{\numberline {2.2}商用网页大模型}{8}{subsection.2.2}%
\contentsline {subsection}{\numberline {2.3}其他}{9}{subsection.2.3}%
