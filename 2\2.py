# # 数的基本运算
# a = 9
# print(a + a)
# print(a * a)
# print(a ** 2.5)
# print(a / 3)

# # 整除：9 // 2 = 4 ... 1
# print(a // 2) # = 4
# print(a % 2) # 1

# print((a % 2) + (a // 2) * 2 - a)  # = a

# 向量
L = [1, 2, 3, 4, 5, 6] # 6维的向量
K = [1, 1, 1, 1, 1, 1]

# 向量的遍历
# (1) 按元素遍历
# for i in L:
#     i = i + 1 
#     print(i)

# (2) 按下标遍历
# print(L[1]) # 正着数从 0 开始
# print(L[-2]) # 反着数从 -1 开始

# for i in range(len(L)):
# print(L[i])

# 向量修改
# 按下标可以修改
for i in range(len(L)):
    L[i] = L[i] - 1
print(L)

L = [1, 2, 3, 4, 5, 6] # 6维的向量
# 按元素遍历是无法逐个修改元素的
for i in L:
    i = i - 1
print(L)



        
